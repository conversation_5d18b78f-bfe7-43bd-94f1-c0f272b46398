// Replace all \uXXXX sequences in the entire file (not only string literals).
// WARNING: This will also modify regex literals and any unicode escapes in code.
// Proceed only if you intend to decode all occurrences.

const fs = require('fs');
const path = require('path');

const targetPath = path.resolve(process.cwd(), 'proxyFilter.js');
const src = fs.readFileSync(targetPath, 'utf8');

function decodeAll(code){
  return code.replace(/\\u([0-9a-fA-F]{4})/g, (_, hex) => String.fromCharCode(parseInt(hex, 16)));
}

const before = (src.match(/\\u[0-9a-fA-F]{4}/g)||[]).length;
const out = decodeAll(src);
const after = (out.match(/\\u[0-9a-fA-F]{4}/g)||[]).length;

if (out !== src) {
  fs.writeFileSync(targetPath, out, 'utf8');
}

console.log(JSON.stringify({ before, after, changed: out !== src }));

