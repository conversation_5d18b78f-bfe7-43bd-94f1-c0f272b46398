// Decode \uXXXX sequences inside JavaScript string literals in a target file.
// This script keeps code semantics intact by only transforming contents of
// string literals (single, double quotes, and template literals).
// All comments are in English.

import fs from 'node:fs';
import path from 'node:path';

const targetPath = path.resolve(process.cwd(), 'proxyFilter.js');
const src = fs.readFileSync(targetPath, 'utf8');

function decodeUnicodeEscapes(s) {
  return s.replace(/\\u([0-9a-fA-F]{4})/g, (_, hex) =>
    String.fromCharCode(parseInt(hex, 16))
  );
}

// States for scanning
const STATE = {
  OUT: 0,
  IN_SQ: 1,      // single-quoted string
  IN_DQ: 2,      // double-quoted string
  IN_TPL: 3      // template literal (raw scanning; placeholders kept as-is)
};

function transform(code) {
  let out = '';
  let i = 0;
  let state = STATE.OUT;
  let buf = '';

  while (i < code.length) {
    const ch = code[i];

    if (state === STATE.OUT) {
      if (ch === "'") {
        state = STATE.IN_SQ;
        out += ch;
        buf = '';
        i++;
        continue;
      }
      if (ch === '"') {
        state = STATE.IN_DQ;
        out += ch;
        buf = '';
        i++;
        continue;
      }
      if (ch === '`') {
        state = STATE.IN_TPL;
        out += ch;
        buf = '';
        i++;
        continue;
      }
      out += ch;
      i++;
      continue;
    }

    // Inside single-quoted string
    if (state === STATE.IN_SQ) {
      if (ch === '\\') {
        // Preserve escape sequences literally in buffer
        if (i + 1 < code.length) {
          buf += ch + code[i + 1];
          i += 2;
        } else {
          buf += ch;
          i++;
        }
        continue;
      }
      if (ch === "'") {
        // End of single-quoted string
        out += decodeUnicodeEscapes(buf) + ch;
        buf = '';
        state = STATE.OUT;
        i++;
        continue;
      }
      buf += ch;
      i++;
      continue;
    }

    // Inside double-quoted string
    if (state === STATE.IN_DQ) {
      if (ch === '\\') {
        if (i + 1 < code.length) {
          buf += ch + code[i + 1];
          i += 2;
        } else {
          buf += ch;
          i++;
        }
        continue;
      }
      if (ch === '"') {
        out += decodeUnicodeEscapes(buf) + ch;
        buf = '';
        state = STATE.OUT;
        i++;
        continue;
      }
      buf += ch;
      i++;
      continue;
    }

    // Inside template literal (raw scanning; do not parse ${...})
    if (state === STATE.IN_TPL) {
      if (ch === '\\') {
        if (i + 1 < code.length) {
          buf += ch + code[i + 1];
          i += 2;
        } else {
          buf += ch;
          i++;
        }
        continue;
      }
      if (ch === '`') {
        out += decodeUnicodeEscapes(buf) + ch;
        buf = '';
        state = STATE.OUT;
        i++;
        continue;
      }
      // Keep everything else in buffer (including ${...} content)
      buf += ch;
      i++;
      continue;
    }
  }

  // Flush any trailing buffer (should not normally happen)
  if (buf) {
    out += decodeUnicodeEscapes(buf);
  }
  return out;
}

const result = transform(src);
if (result !== src) {
  fs.writeFileSync(targetPath, result, 'utf8');
  console.log('proxyFilter.js updated: Unicode escapes in string literals decoded.');
} else {
  console.log('No changes needed.');
}

