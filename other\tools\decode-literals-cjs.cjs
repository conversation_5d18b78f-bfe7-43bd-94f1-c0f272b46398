// Decode \uXXXX sequences inside JavaScript string literals in proxyFilter.js (CommonJS version)
// Only transforms contents inside string literals ('...", "...", `...`).
// Does not touch regex literals or code outside strings.
// All comments are in English.

const fs = require('fs');
const path = require('path');

const targetPath = path.resolve(process.cwd(), 'proxyFilter.js');
const src = fs.readFileSync(targetPath, 'utf8');

const STATE = { OUT:0, SQ:1, DQ:2, TPL:3 };

function decodeUnicodeEscapes(s) {
  return s.replace(/\\u([0-9a-fA-F]{4})/g, (_, hex) => String.fromCharCode(parseInt(hex, 16)));
}

function transform(code){
  let state = STATE.OUT;
  let out = '';
  let buf = '';
  let i = 0;

  const flush = () => {
    if (buf.length) {
      out += decodeUnicodeEscapes(buf);
      buf = '';
    }
  };

  while (i < code.length) {
    const ch = code[i];
    if (state === STATE.OUT) {
      if (ch === "'") { state = STATE.SQ; out += ch; i++; continue; }
      if (ch === '"') { state = STATE.DQ; out += ch; i++; continue; }
      if (ch === '`') { state = STATE.TPL; out += ch; i++; continue; }
      out += ch; i++; continue;
    }
    if (state === STATE.SQ) {
      if (ch === '\\') {
        if (i+1 < code.length) { buf += ch + code[i+1]; i += 2; } else { buf += ch; i++; }
        continue;
      }
      if (ch === "'") { out += decodeUnicodeEscapes(buf) + ch; buf=''; state = STATE.OUT; i++; continue; }
      buf += ch; i++; continue;
    }
    if (state === STATE.DQ) {
      if (ch === '\\') {
        if (i+1 < code.length) { buf += ch + code[i+1]; i += 2; } else { buf += ch; i++; }
        continue;
      }
      if (ch === '"') { out += decodeUnicodeEscapes(buf) + ch; buf=''; state = STATE.OUT; i++; continue; }
      buf += ch; i++; continue;
    }
    if (state === STATE.TPL) {
      if (ch === '\\') {
        if (i+1 < code.length) { buf += ch + code[i+1]; i += 2; } else { buf += ch; i++; }
        continue;
      }
      if (ch === '`') { out += decodeUnicodeEscapes(buf) + ch; buf=''; state = STATE.OUT; i++; continue; }
      buf += ch; i++; continue;
    }
  }
  flush();
  return out;
}

function countUnicode(code){
  return (code.match(/\\u[0-9a-fA-F]{4}/g)||[]).length;
}

const beforeTotal = countUnicode(src);
const result = transform(src);
const afterTotal = countUnicode(result);

if (result !== src) {
  fs.writeFileSync(targetPath, result, 'utf8');
}

console.log(JSON.stringify({ beforeTotal, afterTotal, changed: result !== src }));

