// Report the number of \uXXXX sequences inside JavaScript string literals only.
// All comments are in English.

import fs from 'node:fs';
import path from 'path';

const targetPath = path.resolve(process.cwd(), 'proxyFilter.js');
const src = fs.readFileSync(targetPath, 'utf8');

const STATE = { OUT:0, SQ:1, DQ:2, TPL:3 };

function countUnicodeInStrings(code){
  let state = STATE.OUT;
  let i=0;
  let buf='';
  let total=0;

  const flush = () => {
    if (!buf) return;
    const m = buf.match(/\\u[0-9a-fA-F]{4}/g);
    if (m) total += m.length;
    buf='';
  };

  while(i<code.length){
    const ch = code[i];
    if (state===STATE.OUT){
      if (ch==='\''){ state=STATE.SQ; i++; continue; }
      if (ch==='\"'){ state=STATE.DQ; i++; continue; }
      if (ch==='`'){ state=STATE.TPL; i++; continue; }
      i++; continue;
    }
    if (state===STATE.SQ){
      if (ch==='\\'){ buf+=ch; if(i+1<code.length){ buf+=code[i+1]; i+=2; } else { i++; } continue; }
      if (ch==='\''){ flush(); state=STATE.OUT; i++; continue; }
      buf+=ch; i++; continue;
    }
    if (state===STATE.DQ){
      if (ch==='\\'){ buf+=ch; if(i+1<code.length){ buf+=code[i+1]; i+=2; } else { i++; } continue; }
      if (ch==='\"'){ flush(); state=STATE.OUT; i++; continue; }
      buf+=ch; i++; continue;
    }
    if (state===STATE.TPL){
      if (ch==='\\'){ buf+=ch; if(i+1<code.length){ buf+=code[i+1]; i+=2; } else { i++; } continue; }
      if (ch==='`'){ flush(); state=STATE.OUT; i++; continue; }
      buf+=ch; i++; continue;
    }
  }
  flush();
  return total;
}

console.log('unicodeInStringLiterals='+countUnicodeInStrings(src));

