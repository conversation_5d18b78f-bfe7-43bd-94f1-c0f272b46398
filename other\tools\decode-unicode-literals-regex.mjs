// Decode \uXXXX sequences inside JavaScript string literals using a regex match of string literals.
// Handles single, double, and template literals. It does NOT touch regex literals or code outside strings.
// All comments are in English.

import fs from 'node:fs';
import path from 'node:path';

const targetPath = path.resolve(process.cwd(), 'proxyFilter.js');
const src = fs.readFileSync(targetPath, 'utf8');

const STRING_LITERAL_REGEX = /'(?:\\.|[^'\\])*'|"(?:\\.|[^"\\])*"|`(?:\\.|[^`\\])*`/g;

function decodeUnicodeInStringLiteral(lit) {
  // lit includes the surrounding quotes/backticks
  const quote = lit[0];
  const body = lit.slice(1, -1);
  const decodedBody = body.replace(/\\u([0-9a-fA-F]{4})/g, (_, hex) => String.fromCharCode(parseInt(hex, 16)));
  return quote + decodedBody + quote;
}

let changed = false;
const out = src.replace(STRING_LITERAL_REGEX, (m) => {
  const replaced = decodeUnicodeInStringLiteral(m);
  if (replaced !== m) changed = true;
  return replaced;
});

if (changed) {
  fs.writeFileSync(targetPath, out, 'utf8');
  console.log('proxyFilter.js updated by regex-based string-literal decoder.');
} else {
  console.log('No changes made by regex-based decoder.');
}

